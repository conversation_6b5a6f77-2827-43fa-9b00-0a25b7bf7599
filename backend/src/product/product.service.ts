import { 
  Injectable, 
  NotFoundException, 
  BadRequestException,
  ConflictException 
} from '@nestjs/common';
import { Product, Prisma } from '@prisma/client';
import { PrismaService } from '../prisma/prisma.service';
import { CreateProductDto } from './dto/create-product.dto';
import { SearchProductDto } from './dto/search-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { IProductsService, ProductSearchResult } from './product.interface';

@Injectable()
export class ProductService implements IProductsService {
  constructor(private prisma: PrismaService) {}

  async create(createProductDto: CreateProductDto): Promise<Product> {
    try {
      // Check if product with same name already exists
      const existingProduct = await this.prisma.product.findFirst({
        where: {
          name: {
            equals: createProductDto.name,
            mode: 'insensitive',
          },
        },
      });

      if (existingProduct) {
        throw new ConflictException('Product with this name already exists');
      }

      const product = await this.prisma.product.create({
        data: {
          name: createProductDto.name,
          shortDescription: createProductDto.shortDescription,
          price: createProductDto.price,
          imageUrl: createProductDto.imageUrl,
          stock: createProductDto.stock || 0,
          isActive: createProductDto.isActive !== undefined ? createProductDto.isActive : true,
        },
      });

      return product;
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException('Failed to create product');
    }
  }

  async findAll(searchDto: SearchProductDto): Promise<ProductSearchResult> {
    const { search, minPrice, maxPrice, isActive, page = 1, limit = 10 } = searchDto;

    // Build where clause
    const where: Prisma.ProductWhereInput = {};

    if (search) {
      where.OR = [
        {
          name: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          shortDescription: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    if (minPrice !== undefined || maxPrice !== undefined) {
      where.price = {};
      if (minPrice !== undefined) {
        where.price.gte = minPrice;
      }
      if (maxPrice !== undefined) {
        where.price.lte = maxPrice;
      }
    }

    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    try {
      // Get total count for pagination
      const total = await this.prisma.product.count({ where });

      // Calculate pagination
      const totalPages = Math.ceil(total / limit);
      const skip = (page - 1) * limit;

      // Get products
      const products = await this.prisma.product.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      });

      return {
        products,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      throw new BadRequestException('Failed to fetch products');
    }
  }

  async findOne(id: number): Promise<Product | null> {
    try {
      const product = await this.prisma.product.findUnique({
        where: { id },
      });

      if (!product) {
        throw new NotFoundException(`Product with ID ${id} not found`);
      }

      return product;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to fetch product');
    }
  }

  async update(id: number, updateProductDto: UpdateProductDto): Promise<Product> {
    try {
      // Check if product exists
      const existingProduct = await this.prisma.product.findUnique({
        where: { id },
      });

      if (!existingProduct) {
        throw new NotFoundException(`Product with ID ${id} not found`);
      }

      // Check if updating name and if new name conflicts with another product
      if (updateProductDto.name && updateProductDto.name !== existingProduct.name) {
        const nameConflict = await this.prisma.product.findFirst({
          where: {
            name: {
              equals: updateProductDto.name,
              mode: 'insensitive',
            },
            id: {
              not: id,
            },
          },
        });

        if (nameConflict) {
          throw new ConflictException('Product with this name already exists');
        }
      }

      const updatedProduct = await this.prisma.product.update({
        where: { id },
        data: {
          ...updateProductDto,
        },
      });

      return updatedProduct;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException('Failed to update product');
    }
  }

  async remove(id: number): Promise<Product> {
    try {
      // Check if product exists
      const existingProduct = await this.prisma.product.findUnique({
        where: { id },
        include: {
          cartItems: true,
        },
      });

      if (!existingProduct) {
        throw new NotFoundException(`Product with ID ${id} not found`);
      }

      // Check if product is in any carts
      if (existingProduct.cartItems.length > 0) {
        // Instead of hard delete, mark as inactive
        const updatedProduct = await this.prisma.product.update({
          where: { id },
          data: {
            isActive: false,
          },
        });

        return updatedProduct;
      }

      // If not in any carts, perform hard delete
      const deletedProduct = await this.prisma.product.delete({
        where: { id },
      });

      return deletedProduct;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to delete product');
    }
  }

  async decreaseStock(id: number, quantity: number): Promise<Product> {
    if (quantity <= 0) {
      throw new BadRequestException('Quantity must be greater than 0');
    }

    try {
      const product = await this.prisma.product.findUnique({
        where: { id },
      });

      if (!product) {
        throw new NotFoundException(`Product with ID ${id} not found`);
      }

      if (product.stock < quantity) {
        throw new BadRequestException('Insufficient stock available');
      }

      const updatedProduct = await this.prisma.product.update({
        where: { id },
        data: {
          stock: product.stock - quantity,
        },
      });

      return updatedProduct;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to decrease product stock');
    }
  }

  async increaseStock(id: number, quantity: number): Promise<Product> {
    if (quantity <= 0) {
      throw new BadRequestException('Quantity must be greater than 0');
    }

    try {
      const product = await this.prisma.product.findUnique({
        where: { id },
      });

      if (!product) {
        throw new NotFoundException(`Product with ID ${id} not found`);
      }

      const updatedProduct = await this.prisma.product.update({
        where: { id },
        data: {
          stock: product.stock + quantity,
        },
      });

      return updatedProduct;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to increase product stock');
    }
  }

  // Additional utility methods
  async findActiveProducts(searchDto: SearchProductDto): Promise<ProductSearchResult> {
    return this.findAll({ ...searchDto, isActive: true });
  }

  async findInStock(searchDto: SearchProductDto): Promise<ProductSearchResult> {
    const { search, minPrice, maxPrice, page = 1, limit = 10 } = searchDto;

    const where: Prisma.ProductWhereInput = {
      isActive: true,
      stock: {
        gt: 0,
      },
    };

    if (search) {
      where.OR = [
        {
          name: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          shortDescription: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    if (minPrice !== undefined || maxPrice !== undefined) {
      where.price = {};
      if (minPrice !== undefined) {
        where.price.gte = minPrice;
      }
      if (maxPrice !== undefined) {
        where.price.lte = maxPrice;
      }
    }

    try {
      const total = await this.prisma.product.count({ where });
      const totalPages = Math.ceil(total / limit);
      const skip = (page - 1) * limit;

      const products = await this.prisma.product.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      });

      return {
        products,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      throw new BadRequestException('Failed to fetch in-stock products');
    }
  }

  async getProductStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    inStock: number;
    outOfStock: number;
  }> {
    try {
      const [total, active, inactive, inStock, outOfStock] = await Promise.all([
        this.prisma.product.count(),
        this.prisma.product.count({ where: { isActive: true } }),
        this.prisma.product.count({ where: { isActive: false } }),
        this.prisma.product.count({ where: { stock: { gt: 0 } } }),
        this.prisma.product.count({ where: { stock: 0 } }),
      ]);

      return {
        total,
        active,
        inactive,
        inStock,
        outOfStock,
      };
    } catch (error) {
      throw new BadRequestException('Failed to fetch product statistics');
    }
  }
}
