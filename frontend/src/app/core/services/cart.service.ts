import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { Product } from './product.service';

export interface CartItem {
  id: number;
  productId: number;
  quantity: number;
  product: Product;
  addedAt: string;
}

export interface Cart {
  id: number;
  userId: number;
  items: CartItem[];
  createdAt: string;
  updatedAt: string;
  totalItems: number;
  totalPrice: number;
}

export interface CartResponse {
  success: boolean;
  data: Cart;
  message: string;
}

@Injectable({
  providedIn: 'root'
})
export class CartService {
  private apiUrl = `${environment.apiUrl}/cart`;
  private cartItemsCountSubject = new BehaviorSubject<number>(0);
  public cartItemsCount$ = this.cartItemsCountSubject.asObservable();

  constructor(private http: HttpClient) {
    this.loadCartCount();
  }

  private loadCartCount(): void {
    if (localStorage.getItem('token')) {
      this.getCartItemsCount().subscribe({
        next: (response) => {
          this.cartItemsCountSubject.next(response.count);
        },
        error: (error) => {
          console.error('Failed to load cart count', error);
        }
      });
    }
  }

  getCart(): Observable<CartResponse> {
    return this.http.get<CartResponse>(this.apiUrl).pipe(
      tap(response => {
        if (response.success && response.data) {
          this.cartItemsCountSubject.next(response.data.totalItems);
        }
      })
    );
  }

  addToCart(productId: number, quantity: number = 1): Observable<CartResponse> {
    return this.http.post<CartResponse>(`${this.apiUrl}/add`, { productId, quantity }).pipe(
      tap(response => {
        if (response.success && response.data) {
          this.cartItemsCountSubject.next(response.data.totalItems);
        }
      })
    );
  }

  updateCartItem(itemId: number, quantity: number): Observable<CartResponse> {
    return this.http.patch<CartResponse>(`${this.apiUrl}/item/${itemId}`, { quantity }).pipe(
      tap(response => {
        if (response.success && response.data) {
          this.cartItemsCountSubject.next(response.data.totalItems);
        }
      })
    );
  }

  removeFromCart(itemId: number): Observable<CartResponse> {
    return this.http.delete<CartResponse>(`${this.apiUrl}/item/${itemId}`).pipe(
      tap(response => {
        if (response.success && response.data) {
          this.cartItemsCountSubject.next(response.data.totalItems);
        }
      })
    );
  }

  clearCart(): Observable<any> {
    return this.http.delete<any>(`${this.apiUrl}/clear`).pipe(
      tap(() => {
        this.cartItemsCountSubject.next(0);
      })
    );
  }

  getCartItemsCount(): Observable<{ count: number }> {
    return this.http.get<{ count: number }>(`${this.apiUrl}/count`).pipe(
      tap(response => {
        this.cartItemsCountSubject.next(response.count);
      })
    );
  }
}
