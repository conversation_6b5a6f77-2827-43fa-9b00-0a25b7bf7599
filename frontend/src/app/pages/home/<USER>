<div class="container mx-auto px-4 py-8">
  <!-- Hero Section -->
  <div class="bg-blue-600 text-white rounded-lg p-8 mb-8">
    <h1 class="text-4xl font-bold mb-4">Welcome to Shopie</h1>
    <p class="text-xl mb-6">Discover amazing products at great prices</p>
    <a routerLink="/products" class="bg-white text-blue-600 px-6 py-2 rounded-md font-medium hover:bg-gray-100">
      Shop Now
    </a>
  </div>

  <!-- Loading and Error States -->
  <div *ngIf="isLoading" class="text-center py-8">
    <p>Loading products...</p>
  </div>
  
  <div *ngIf="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
    {{ error }}
  </div>

  <!-- Featured Products Section -->
  <div *ngIf="!isLoading && !error && featuredProducts.length > 0" class="mb-12">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold">Featured Products</h2>
      <a routerLink="/products/featured" class="text-blue-600 hover:underline">View All</a>
    </div>
    
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      <div *ngFor="let product of featuredProducts" class="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
        <img [src]="product.imageUrl || 'assets/images/placeholder.png'" [alt]="product.name" class="w-full h-48 object-cover">
        <div class="p-4">
          <h3 class="font-semibold text-lg mb-2">{{ product.name }}</h3>
          <p class="text-gray-600 mb-2 line-clamp-2">{{ product.description }}</p>
          <div class="flex justify-between items-center">
            <span class="font-bold">${{ product.price.toFixed(2) }}</span>
            <a [routerLink]="['/products', product.id]" class="text-blue-600 hover:underline">View</a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- New Arrivals Section -->
  <div *ngIf="!isLoading && !error && newArrivals.length > 0" class="mb-12">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold">New Arrivals</h2>
      <a routerLink="/products/new" class="text-blue-600 hover:underline">View All</a>
    </div>
    
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      <div *ngFor="let product of newArrivals" class="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
        <img [src]="product.imageUrl || 'assets/images/placeholder.png'" [alt]="product.name" class="w-full h-48 object-cover">
        <div class="p-4">
          <h3 class="font-semibold text-lg mb-2">{{ product.name }}</h3>
          <p class="text-gray-600 mb-2 line-clamp-2">{{ product.description }}</p>
          <div class="flex justify-between items-center">
            <span class="font-bold">${{ product.price.toFixed(2) }}</span>
            <a [routerLink]="['/products', product.id]" class="text-blue-600 hover:underline">View</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
