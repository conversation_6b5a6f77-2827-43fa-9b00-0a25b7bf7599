import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { ProductService, Product } from '../../core/services/product.service';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css']
})
export class HomeComponent implements OnInit {
  featuredProducts: Product[] = [];
  newArrivals: Product[] = [];
  isLoading = true;
  error = '';

  constructor(private productService: ProductService) {}

  ngOnInit(): void {
    this.loadFeaturedProducts();
    this.loadNewArrivals();
  }

  loadFeaturedProducts(): void {
    this.productService.getFeaturedProducts().subscribe({
      next: (response) => {
        this.featuredProducts = response.data.slice(0, 4); // Limit to 4 products
        this.isLoading = false;
      },
      error: (error) => {
        this.error = 'Failed to load featured products';
        this.isLoading = false;
        console.error('Error loading featured products:', error);
      }
    });
  }

  loadNewArrivals(): void {
    this.productService.getNewArrivals().subscribe({
      next: (response) => {
        this.newArrivals = response.data.slice(0, 4); // Limit to 4 products
        this.isLoading = false;
      },
      error: (error) => {
        this.error = 'Failed to load new arrivals';
        this.isLoading = false;
        console.error('Error loading new arrivals:', error);
      }
    });
  }
}
